import withRspack from "next-rspack";

import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    serverComponentsExternalPackages: ["better-auth"],
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        "node:crypto": false,
        "crypto": false,
        "stream": false,
        "util": false,
        "buffer": false,
        "process": false,
      };
    }
    return config;
  },
};

export default withRspack(nextConfig);
